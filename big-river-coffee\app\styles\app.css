:root {
  --aside-width: 400px;
  --cart-aside-summary-height-with-discount: 300px;
  --cart-aside-summary-height: 250px;
  --grid-item-width: 355px;
  --header-height: 120px; /* Reduced header height while keeping logo size */
  --color-dark: #000;
  --color-light: #fff;
}

main {
  padding-top: var(--header-height);
}

/* Background color for all pages except homepage */
body:not(.homepage) {
  background-color: #eeedc1 !important;
}

body:not(.homepage) main {
  background-color: #eeedc1 !important;
}

/* Ensure specific page classes also get the background */
body.collections-page,
body.brew,
body.contact,
body.affiliate,
body.our-story {
  background-color: #eeedc1 !important;
}

body.collections-page main,
body.brew main,
body.contact main,
body.affiliate main,
body.our-story main {
  background-color: #eeedc1 !important;
}

/* Additional coverage for any divs that might override background */
body:not(.homepage) > div,
body:not(.homepage) main > div {
  background-color: inherit;
}

/* Hide header and other elements when video is playing */
body.video-playing header {
  display: none !important;
}

body.video-playing main {
  padding-top: 0 !important;
}

img {
  border-radius: 4px;
}

/* Remove border-radius from homepage icon images - only containers should have outlines */
body.homepage img[src*="/newhomepage/shop_coffee.png"],
body.homepage img[src*="/newhomepage/our_story.png"],
body.homepage img[src*="/newhomepage/connect_with_us.png"],
.home img[src*="/newhomepage/shop_coffee.png"],
.home img[src*="/newhomepage/our_story.png"],
.home img[src*="/newhomepage/connect_with_us.png"] {
  border-radius: 0 !important;
}

/* Target all homepage button images more specifically */
section img[src*="/newhomepage/shop_coffee.png"],
section img[src*="/newhomepage/our_story.png"],
section img[src*="/newhomepage/connect_with_us.png"] {
  border-radius: 0 !important;
}

/*
* --------------------------------------------------
* Non anchor links
* --------------------------------------------------
*/
.link:hover {
  text-decoration: underline;
  cursor: pointer;
}

/*
* --------------------------------------------------
* components/Aside
* --------------------------------------------------
*/
@media (max-width: 45em) {
  html:has(.overlay.expanded) {
    overflow: hidden;
  }
}

aside {
  background: var(--color-primary-800);
  box-shadow: 0 0 50px rgba(0, 0, 0, 0.3);
  height: 100vh;
  width: min(var(--aside-width), 100vw);
  position: fixed;
  right: calc(-1 * var(--aside-width));
  top: 0;
  transition: transform 200ms ease-in-out;
  z-index: 10001; /* Ensure aside is above overlay */
}

aside header {
  align-items: center;
  background: var(--color-primary-800);
  color: white;
  border-bottom: 1px solid var(--color-primary-600);
  display: flex;
  height: var(--header-height);
  justify-content: space-between;
  padding: 0 20px;
}

/* Hide cart header specifically to give more space for products */
aside[aria-modal="cart"] header {
  display: none;
}

aside header h3 {
  margin: 0;
}

aside header .close {
  font-weight: bold;
  color: white;
  opacity: 0.8;
  text-decoration: none;
  transition: all 200ms;
  width: 20px;
}

aside header .close:hover {
  opacity: 1;
  color: #fbbf24; /* yellow-400 for hover effect */
}

aside header h2 {
  margin-bottom: 0.6rem;
  margin-top: 0;
}

aside main {
  margin: 0;
  padding: 1rem;
  height: calc(100vh - var(--header-height));
  overflow-y: auto;
}

aside p {
  margin: 0 0 0.25rem;
}

aside p:last-child {
  margin: 0;
}

aside li {
  margin-bottom: 0.125rem;
}

.overlay {
  background: rgba(0, 0, 0, 0.2);
  bottom: 0;
  left: 0;
  opacity: 0;
  pointer-events: none;
  position: fixed;
  right: 0;
  top: 0;
  transition: opacity 400ms ease-in-out;
  visibility: hidden;
  z-index: 10000; /* Higher than header to ensure proper layering */
}

.overlay .close-outside {
  background: transparent;
  border: none;
  color: transparent;
  height: 100%;
  left: 0;
  position: absolute;
  top: 0;
  width: calc(100% - var(--aside-width));
}

.overlay .light {
  background: rgba(255, 255, 255, 0.5);
}

.overlay .cancel {
  cursor: default;
  height: 100%;
  position: absolute;
  width: 100%;
}

.overlay.expanded {
  opacity: 1;
  pointer-events: auto;
  visibility: visible;
}

/* reveal aside */
.overlay.expanded aside {
  transform: translateX(calc(var(--aside-width) * -1));
}

/* Ensure overlay and aside are completely hidden when not expanded */
.overlay:not(.expanded) {
  display: none;
}

/* Mobile-specific aside adjustments */
@media (max-width: 768px) {
  aside {
    width: 100vw;
    right: -100vw;
  }

  .overlay.expanded aside {
    transform: translateX(-100vw);
  }

  .overlay .close-outside {
    width: 0; /* No close area on mobile since aside takes full width */
  }
}

button.reset {
  border: 0;
  background: inherit;
  font-size: inherit;
}

button.reset > * {
  margin: 0;
}

button.reset:not(:has(> *)) {
  height: 1.5rem;
  line-height: 1.5rem;
}

button.reset:hover:not(:has(> *)) {
  text-decoration: underline;
  cursor: pointer;
}

/*
* --------------------------------------------------
* components/Header
* --------------------------------------------------
*/
.header {
  align-items: center;
  background: #3a5c5c;
  display: flex;
  height: var(--header-height);
  padding: 0 1rem;
  position: sticky;
  top: 0;
  z-index: 1000; /* Lower than overlay/aside to prevent conflicts */
}

.header-menu-mobile-toggle {
  @media (min-width: 48em) {
    display: none;
  }
}

.header-menu-mobile {
  display: flex;
  flex-direction: column;
  grid-gap: 1rem;
}

/* Mobile-specific header improvements */
@media (max-width: 768px) {
  :root {
    --header-height: 100px; /* Reduced mobile header height */
  }

  .header {
    padding: 0 0.75rem;
  }

  /* Ensure proper vertical centering on mobile */
  .header .max-w-7xl {
    display: flex;
    align-items: center;
    height: 100%;
  }

  .header .flex.items-center.justify-between {
    width: 100%;
    align-items: center;
  }
}

/* Ensure mobile menu items have proper touch targets */
.header-menu-mobile a {
  min-height: 48px;
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.header-menu-mobile a:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.header-menu-desktop {
  display: none;
  grid-gap: 1rem;
  @media (min-width: 45em) {
    display: flex;
    grid-gap: 1rem;
    margin-left: 3rem;
  }
}

.header-menu-item {
  cursor: pointer;
}

.header-ctas {
  align-items: center;
  display: flex;
  grid-gap: 1rem;
  margin-left: auto;
}

.header-ctas > * {
  min-width: fit-content;
}

/* Ensure all header links are white */
.header a,
header a,
.header-menu-desktop a,
.header-ctas a,
.header nav a {
  color: white !important;
}

.header a:hover,
header a:hover,
.header-menu-desktop a:hover,
.header-ctas a:hover,
.header nav a:hover {
  color: #fb923c !important; /* orange-400 */
}


/*
* --------------------------------------------------
* components/Footer
* --------------------------------------------------
*/
.footer {
  background: var(--color-dark);
  margin-top: auto;
}

.footer-menu {
  justify-content: center;
  display: flex;
  flex-wrap: wrap;
  grid-gap: 1rem;
  padding: 1rem;
}

.footer-menu a {
  color: var(--color-light);
  min-width: fit-content;
}

/* Ensure all footer links are white */
.footer a,
footer a {
  color: white !important;
}

.footer a:hover,
footer a:hover {
  color: #fb923c !important; /* orange-400 */
}

/*
* --------------------------------------------------
* components/Cart
* --------------------------------------------------
*/
.cart-main {
  /* Remove conflicting styles to let Tailwind handle layout */
}

.cart-line {
  display: flex;
  padding: 0.75rem 0;
}

.cart-line img {
  height: 100%;
  display: block;
  margin-right: 0.75rem;
}

.cart-summary-page {
  position: relative;
}

.cart-summary-aside {
  background: white;
  border-top: 1px solid var(--color-dark);
  bottom: 0;
  padding-top: 0.75rem;
  position: absolute;
  width: calc(var(--aside-width) - 40px);
}

.cart-line-quantity {
  display: flex;
}

.cart-discount {
  align-items: center;
  display: flex;
  margin-top: 0.25rem;
}

.cart-subtotal {
  align-items: center;
  display: flex;
}
/*
* --------------------------------------------------
* components/Search
* --------------------------------------------------
*/
.predictive-search {
  height: calc(100vh - var(--header-height) - 40px);
  overflow-y: auto;
}

.predictive-search-form {
  background: var(--color-light);
  position: sticky;
  top: 0;
}

.predictive-search-result {
  margin-bottom: 2rem;
}

.predictive-search-result h5 {
  text-transform: uppercase;
}

.predictive-search-result-item {
  margin-bottom: 0.5rem;
}

.predictive-search-result-item a {
  align-items: center;
  display: flex;
}

.predictive-search-result-item a img {
  margin-right: 0.75rem;
  height: 100%;
}

.search-result {
  margin-bottom: 1.5rem;
}

.search-results-item {
  margin-bottom: 0.5rem;
}

.search-results-item a {
  display: flex;
  flex: row;
  align-items: center;
  gap: 1rem;
}

/*
* --------------------------------------------------
* routes/__index
* --------------------------------------------------
*/
.featured-collection {
  display: block;
  margin-bottom: 2rem;
  position: relative;
}

.featured-collection-image {
  aspect-ratio: 1 / 1;
  @media (min-width: 45em) {
    aspect-ratio: 16 / 9;
  }
}

.featured-collection img {
  height: auto;
  max-height: 100%;
  object-fit: cover;
}

.recommended-products-grid {
  display: grid;
  grid-gap: 1.5rem;
  grid-template-columns: repeat(2, 1fr);
  @media (min-width: 45em) {
    grid-template-columns: repeat(4, 1fr);
  }
}

.recommended-product img {
  height: auto;
}

/*
* --------------------------------------------------
* routes/collections._index.tsx
* --------------------------------------------------
*/
.collections-grid {
  display: grid;
  grid-gap: 1.5rem;
  grid-template-columns: repeat(auto-fit, minmax(var(--grid-item-width), 1fr));
  margin-bottom: 2rem;
}

.collection-item img {
  height: auto;
}

/*
* --------------------------------------------------
* routes/collections.$handle.tsx
* --------------------------------------------------
*/
.collection-description {
  margin-bottom: 1rem;
  max-width: 95%;
  @media (min-width: 45em) {
    max-width: 600px;
  }
}

.products-grid {
  display: grid;
  grid-gap: 1.5rem;
  grid-template-columns: repeat(auto-fit, minmax(var(--grid-item-width), 1fr));
  margin-bottom: 2rem;
}

.product-item img {
  height: auto;
  width: 100%;
}

/* Mobile-specific product grid improvements */
@media (max-width: 768px) {
  .products-grid {
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 1rem;
  }

  /* Improve mobile product cards */
  .product-item {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }

  .product-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }

  /* Optimize product images for mobile */
  .product-item img {
    aspect-ratio: 1;
    object-fit: cover;
    height: auto;
    max-height: 200px;
  }

  /* Better mobile typography */
  .product-item h3 {
    font-size: 0.9rem;
    line-height: 1.3;
    margin-bottom: 0.5rem;
  }

  .product-item .price {
    font-size: 0.85rem;
    font-weight: 600;
  }
}

/* Mobile-specific hero section improvements */
@media (max-width: 768px) {
  /* Homepage hero mobile optimizations */
  .hero-section {
    overflow: hidden;
  }

  .hero-section img {
    width: 100%;
    height: auto;
    object-fit: cover;
    min-height: 300px;
  }

  /* Mobile CTA buttons */
  .hero-buttons {
    padding: 1rem;
    gap: 0.75rem;
  }

  .hero-buttons a {
    padding: 1rem 1.5rem;
    font-size: 1rem;
    border-radius: 12px;
    min-height: 52px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
  }

  /* Mobile section navigation */
  .section-nav {
    padding: 0.5rem 1rem;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .section-nav::-webkit-scrollbar {
    display: none;
  }

  .section-nav button {
    flex-shrink: 0;
    white-space: nowrap;
    min-width: fit-content;
  }

  /* Mobile touch improvements */
  button, a, [role="button"] {
    min-height: 44px;
    min-width: 44px;
  }

  /* Mobile form improvements */
  input, select, textarea {
    font-size: 16px; /* Prevents zoom on iOS */
    min-height: 44px;
  }

  /* Mobile cart and search improvements are now handled by the general aside styles above */

  /* Improve mobile scrolling performance */
  .overflow-x-auto {
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .overflow-x-auto::-webkit-scrollbar {
    display: none;
  }
}

/* Utility classes for mobile */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Prevent layout shifts and improve stability */
.hero-section {
  contain: layout style;
}

.section-nav {
  contain: layout style;
  will-change: auto;
}

.sticky {
  position: -webkit-sticky;
  position: sticky;
}

/* Improve scroll performance */
.smooth-scroll {
  scroll-behavior: smooth;
}

/* Prevent scroll issues */
.scroll-container {
  overflow-x: auto;
  overflow-y: hidden;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.scroll-container::-webkit-scrollbar {
  display: none;
}

/* Fix section navigation scrollbar */
.overflow-x-auto::-webkit-scrollbar {
  height: 0px;
  background: transparent;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
  background: transparent;
}

/* Ensure proper image sizing for K-cups */
.kcup-image-container {
  max-width: 400px;
  max-height: 400px;
}

.kcup-image-container img {
  object-fit: contain !important;
  padding: 1rem;
}

/* Mobile hero image optimization */
@media (max-width: 640px) {
  .mobile-hero-image {
    width: 100% !important;
    height: auto !important;
    object-fit: contain !important;
    display: block !important;
  }
}

  /* Ensure mobile navigation is touch-friendly and stable */
  .mobile-nav-grid {
    touch-action: manipulation;
    display: grid !important;
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 0.75rem !important;
  }

  .mobile-nav-grid button {
    min-height: 52px !important;
    min-width: 48px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }

  /* Force mobile layout to stay stable and prevent flickering */
  @media (max-width: 1023px) {
    .collections-mobile-nav {
      display: block !important;
      visibility: visible !important;
      opacity: 1 !important;
    }

    .collections-desktop-nav {
      display: none !important;
      visibility: hidden !important;
      opacity: 0 !important;
    }
  }

  @media (min-width: 1024px) {
    .collections-mobile-nav {
      display: none !important;
      visibility: hidden !important;
      opacity: 0 !important;
    }

    .collections-desktop-nav {
      display: flex !important;
      visibility: visible !important;
      opacity: 1 !important;
    }
  }

  /* Prevent layout shift during hydration */
  .collections-mobile-nav,
  .collections-desktop-nav {
    transition: none !important;
  }

  /* Ensure navigation containers are stable */
  .collections-mobile-nav .mobile-nav-grid {
    display: grid !important;
    grid-template-columns: repeat(2, 1fr) !important;
  }

/* Product form text color overrides */
.product-form h3,
.product-form label,
.product-form span:not(.text-white) {
  color: #000000 !important;
}

.product-form button:not(.bg-army-600):not([disabled]) {
  color: #000000 !important;
}

/* Subscription dropdown text overrides */
.product-form .space-y-3 h3,
.product-form .space-y-4 h3 {
  color: #000000 !important;
}

/* Ensure all form text is black */
.product-form * {
  color: #000000 !important;
}

/* Except for selected states and specific elements */
.product-form .bg-army-600,
.product-form .bg-army-600 *,
.product-form .text-white,
.product-form .text-orange-600,
.product-form .text-gray-400,
.product-form svg {
  color: inherit !important;
}

/* Ensure mobile nav and cart are always on top */
.overlay {
  z-index: 99999 !important;
}

aside {
  z-index: 100000 !important;
}

/* Ensure product page content doesn't interfere with nav/cart */
.product-page-content {
  position: relative;
  z-index: 1;
}

/* Force mobile nav and cart to be visible on all pages */
@media (max-width: 768px) {
  .overlay {
    z-index: 999999 !important;
    position: fixed !important;
  }

  aside {
    z-index: 1000000 !important;
    position: fixed !important;
  }

  /* Ensure header is below overlay/aside */
  .header {
    z-index: 999 !important;
  }
}

/* Enhanced product card animations */
.group:hover .aspect-square img {
  transform: scale(1.1);
}

.group:hover .product-info {
  transform: translateY(-2px);
}

/* Smooth transitions for all card elements */
.group * {
  transition: all 0.5s ease-out;
}

/* Enhanced shadow on hover */
.group:hover {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(0, 0, 0, 0.06);
}

/*
* --------------------------------------------------
* routes/products.$handle.tsx
* --------------------------------------------------
*/
.product {
  display: grid;
  @media (min-width: 45em) {
    grid-template-columns: 1fr 1fr;
    grid-gap: 4rem;
  }
}

.product h1 {
  margin-top: 0;
}

.product-image img {
  height: auto;
  width: 100%;
}

.product-main {
  align-self: start;
  position: sticky;
  top: 6rem;
}

.product-price-on-sale {
  display: flex;
  grid-gap: 0.5rem;
}

.product-price-on-sale s {
  opacity: 0.5;
}

.product-options-grid {
  display: flex;
  flex-wrap: wrap;
  grid-gap: 0.75rem;
}

.product-options-item,
.product-options-item:disabled {
  padding: 0.25rem 0.5rem;
  background-color: transparent;
  font-size: 1rem;
  font-family: inherit;
}

.product-option-label-swatch {
  width: 1.25rem;
  height: 1.25rem;
  margin: 0.25rem 0;
}

.product-option-label-swatch img {
  width: 100%;
}

/*
* --------------------------------------------------
* routes/blog._index.tsx
* --------------------------------------------------
*/
.blog-grid {
  display: grid;
  grid-gap: 1.5rem;
  grid-template-columns: repeat(auto-fit, minmax(var(--grid-item-width), 1fr));
  margin-bottom: 2rem;
}

.blog-article-image {
  aspect-ratio: 3/2;
  display: block;
}

.blog-article-image img {
  height: 100%;
}

/*
* --------------------------------------------------
* routes/blog.$articlehandle.tsx
* --------------------------------------------------
*/
.article img {
  height: auto;
  width: 100%;
}

/*
* --------------------------------------------------
* routes/account
* --------------------------------------------------
*/

.account-logout {
  display: inline-block;
}
