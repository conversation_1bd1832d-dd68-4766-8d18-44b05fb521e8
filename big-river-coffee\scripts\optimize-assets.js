#!/usr/bin/env node

/**
 * Asset Optimization Script
 * Compresses stillframe images, re-compresses videos, and converts images to WebP
 */

import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import sharp from 'sharp';
import imagemin from 'imagemin';
import imageminPngquant from 'imagemin-pngquant';
import imageminMozjpeg from 'imagemin-mozjpeg';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.join(__dirname, '..');

// Configuration
const STILLFRAME_IMAGES = [
  'public/newhomepage/homepage_stillframe.png',
  'public/newhomepage/aboutus_stillframe.png', 
  'public/newhomepage/shop_stillframe.png'
];

const IMAGES_TO_CONVERT = [
  // newhomepage folder images
  'public/newhomepage/Big_River_Coffee_Rectangle_Logo.png',
  'public/newhomepage/MELBlount_LOGO.png',
  'public/newhomepage/aboutus1.png',
  'public/newhomepage/aboutus2.png',
  'public/newhomepage/aboutus3.png',
  'public/newhomepage/figmass.png',
  'public/newhomepage/our_story.png',
  'public/newhomepage/shop_coffee.png',
  'public/newhomepage/social_media.png',
  'public/newhomepage/mobile_homeage_bg_sf.png',

  // Main public folder images that don't have WebP versions
  'public/brc.png',
  'public/our_story_wordart.png'
];

async function fileExists(filePath) {
  try {
    await fs.access(filePath);
    return true;
  } catch {
    return false;
  }
}

async function getFileSize(filePath) {
  try {
    const stats = await fs.stat(filePath);
    return stats.size;
  } catch {
    return 0;
  }
}

function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

async function compressStillframes() {
  console.log('🖼️  Compressing stillframe images...');
  let totalSaved = 0;
  
  for (const imagePath of STILLFRAME_IMAGES) {
    const fullPath = path.join(projectRoot, imagePath);
    
    if (await fileExists(fullPath)) {
      const originalSize = await getFileSize(fullPath);
      console.log(`   Processing: ${path.basename(imagePath)}`);
      
      try {
        // Compress PNG with pngquant
        const compressed = await imagemin([fullPath], {
          plugins: [
            imageminPngquant({
              quality: [0.6, 0.8], // Good quality range for stillframes
              speed: 1 // Better compression
            })
          ]
        });
        
        if (compressed.length > 0) {
          await fs.writeFile(fullPath, compressed[0].data);
          const newSize = await getFileSize(fullPath);
          const saved = originalSize - newSize;
          totalSaved += saved;
          
          console.log(`     ✅ Compressed: ${formatBytes(originalSize)} → ${formatBytes(newSize)} (saved ${formatBytes(saved)})`);
        }
      } catch (error) {
        console.error(`     ❌ Error compressing ${imagePath}:`, error.message);
      }
    } else {
      console.log(`     ⚠️  File not found: ${imagePath}`);
    }
  }
  
  console.log(`💾 Total saved from stillframes: ${formatBytes(totalSaved)}\n`);
  return totalSaved;
}

async function convertToWebP() {
  console.log('🔄 Converting images to WebP format...');
  let totalConverted = 0;
  
  for (const imagePath of IMAGES_TO_CONVERT) {
    const fullPath = path.join(projectRoot, imagePath);
    
    if (await fileExists(fullPath)) {
      const originalSize = await getFileSize(fullPath);
      const webpPath = fullPath.replace(/\.(png|jpg|jpeg)$/i, '.webp');
      
      console.log(`   Converting: ${path.basename(imagePath)}`);
      
      try {
        await sharp(fullPath)
          .webp({ 
            quality: 85, // High quality for important images
            effort: 6   // Maximum compression effort
          })
          .toFile(webpPath);
        
        const webpSize = await getFileSize(webpPath);
        const saved = originalSize - webpSize;
        totalConverted++;
        
        console.log(`     ✅ Created WebP: ${formatBytes(originalSize)} → ${formatBytes(webpSize)} (saved ${formatBytes(saved)})`);
      } catch (error) {
        console.error(`     ❌ Error converting ${imagePath}:`, error.message);
      }
    } else {
      console.log(`     ⚠️  File not found: ${imagePath}`);
    }
  }
  
  console.log(`🎯 Converted ${totalConverted} images to WebP format\n`);
  return totalConverted;
}

// Main execution function
async function main() {
  console.log('🚀 Starting Big River Coffee Asset Optimization...\n');

  try {
    // Step 1: Compress stillframe images
    const savedFromStillframes = await compressStillframes();

    // Step 2: Convert images to WebP
    const convertedImages = await convertToWebP();

    console.log('✅ Asset optimization completed successfully!');
    console.log('\n📊 Summary:');
    console.log(`   • Stillframes optimized with ${formatBytes(savedFromStillframes)} saved`);
    console.log(`   • ${convertedImages} images converted to WebP format`);
    console.log('\n🎯 Next steps:');
    console.log('   1. Update OptimizedVideo components to use stillframes');
    console.log('   2. Test video loading performance');
    console.log('   3. Consider additional video compression if needed\n');

  } catch (error) {
    console.error('❌ Error during optimization:', error);
    process.exit(1);
  }
}

// Run the script
main();
