import {Suspense, useState, useEffect} from 'react';
import {Await, NavLink, useAsyncValue} from 'react-router';
import {
  type CartViewPayload,
  useAnalytics,
  useOptimisticCart,
} from '@shopify/hydrogen';
import type {HeaderQuery, CartApiQueryFragment} from 'storefrontapi.generated';
import {useAside} from '~/components/Aside';

interface HeaderProps {
  header: HeaderQuery;
  cart: Promise<CartApiQueryFragment | null>;
  isLoggedIn: Promise<boolean>;
  publicStoreDomain: string;
}

type Viewport = 'desktop' | 'mobile';

export function Header({
  header,
  isLoggedIn,
  cart,
  publicStoreDomain,
}: HeaderProps) {
  const {shop, menu} = header;
  const [isScrolled, setIsScrolled] = useState(false);
  const [isHeaderVisible, setIsHeaderVisible] = useState(false); // Start hidden
  const [lastScrollY, setLastScrollY] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;

      // Set scrolled state for styling
      setIsScrolled(currentScrollY > 50);

      // Universal scroll-triggered header behavior
      const threshold = 150; // Show header after scrolling 150px

      if (currentScrollY > threshold) {
        // Show header when scrolled past threshold
        setIsHeaderVisible(true);
      } else {
        // Hide header when at top of page
        setIsHeaderVisible(false);
      }

      setLastScrollY(currentScrollY);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [lastScrollY]);

  return (
    <>
      {/* Summer Sale Banner - Always sticky */}
      <div className="fixed top-0 left-0 right-0 z-50 text-white py-1.5 px-4" style={{ backgroundColor: '#7895a4' }}>
        <div className="max-w-7xl mx-auto text-center">
          <p className="text-sm font-semibold">
            🔥 30% OFF EVERYTHING WITH CODE{' '}
            <span className="bg-white px-2 py-1 rounded font-bold text-xs" style={{ color: '#7895a4' }}>
              SUMMER30
            </span>
            {' '}🔥
          </p>
        </div>
      </div>

      {/* Main Header - Universal scroll-triggered behavior */}
      <header
        className={`fixed left-0 right-0 z-40 transition-all duration-300 ${
          isHeaderVisible ? 'translate-y-0' : '-translate-y-full'
        }`}
        style={{
          top: '32px', // Account for banner height
          height: 'calc(var(--header-height) - 32px)',
          backgroundColor: '#3A5C5C',
          background: '#3A5C5C'
        }}
      >
        <div className="max-w-7xl mx-auto relative px-4 sm:px-6 lg:px-8 h-full">
          <div className="flex items-center justify-between h-full">
            {/* Logo */}
            <div className="flex justify-start items-center h-full">
              <NavLink
                to="/"
                prefetch="intent"
                end
                className="relative group flex items-center h-full"
              >
                <img
                  src="/headerlogo.svg"
                  alt="Big River Coffee"
                  className="transition-all duration-300 group-hover:scale-105 h-24 sm:h-32 lg:h-36 w-auto"
                  width="auto"
                  height="140"
                />
              </NavLink>
            </div>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-8">
              <HeaderMenu
                menu={null}
                viewport="desktop"
                primaryDomainUrl={header.shop.primaryDomain.url}
                publicStoreDomain={publicStoreDomain}
              />
            </div>

            {/* Right Side CTAs */}
            <div className="flex-1 flex justify-end">
              <HeaderCtas isLoggedIn={isLoggedIn} cart={cart} />
            </div>
          </div>
        </div>
      </header>
    </>
  );
}

export function HeaderMenu({
  menu,
  primaryDomainUrl,
  viewport,
  publicStoreDomain,
}: {
  menu: HeaderProps['header']['menu'];
  primaryDomainUrl: HeaderProps['header']['shop']['primaryDomain']['url'];
  viewport: Viewport;
  publicStoreDomain: HeaderProps['publicStoreDomain'];
}) {
  const {close} = useAside();

  if (viewport === 'mobile') {
    // Debug: Log menu state
    console.log('Mobile menu rendering:', {
      menuExists: !!menu,
      fallbackItems: FALLBACK_HEADER_MENU.items.length,
      menuItems: menu?.items?.length || 0
    });

    return (
      <nav className="header-menu-mobile" role="navigation">
        <NavLink
          end
          onClick={close}
          prefetch="intent"
          to="/"
          className="header-menu-item"
        >
          Home
        </NavLink>
        {/* Rewards Link */}
        <a
          href="https://bigriverbe.myshopify.com/pages/loyalty-program"
          target="_blank"
          rel="noopener noreferrer"
          className="header-menu-item"
          onClick={close}
        >
          Rewards
        </a>

        {/* Always use fallback menu to ensure all pages show */}
        {FALLBACK_HEADER_MENU.items.map((item) => {
          if (!item.url) return null;

          let url = item.url;

          // Handle external URLs
          if (item.url.includes('myshopify.com') ||
              item.url.includes(publicStoreDomain) ||
              item.url.includes(primaryDomainUrl)) {
            try {
              url = new URL(item.url).pathname;
            } catch (e) {
              url = item.url;
            }
          }

          // Redirect K-cup product links to our new K-cups section
          if (url.includes('/products/big-river-k-cups') ||
              (item.title && item.title.toLowerCase().includes('k-cup'))) {
            url = '/collections/all?section=kcups';
          }

          // Fix subscriptions URL for mobile
          if (url.includes('#section-subscriptions')) {
            url = '/collections/all?section=subscriptions';
          }

          return (
            <NavLink
              end
              key={item.id}
              onClick={close}
              prefetch="intent"
              to={url}
              className="header-menu-item"
            >
              {item.title}
            </NavLink>
          );
        })}
      </nav>
    );
  }

  return (
    <nav className="flex items-center space-x-8" role="navigation">
      {/* Always use fallback menu to ensure all pages show */}
      {FALLBACK_HEADER_MENU.items.map((item) => {
        if (!item.url) return null;

        let url = item.url;

        // Handle external URLs
        if (item.url.includes('myshopify.com') ||
            item.url.includes(publicStoreDomain) ||
            item.url.includes(primaryDomainUrl)) {
          try {
            url = new URL(item.url).pathname;
          } catch (e) {
            url = item.url;
          }
        }

        // Redirect K-cup product links to our new K-cups section
        if (url.includes('/products/big-river-k-cups') ||
            (item.title && item.title.toLowerCase().includes('k-cup'))) {
          url = '/collections/all?section=kcups';
        }

        // Fix subscriptions URL for desktop
        if (url.includes('#section-subscriptions')) {
          url = '/collections/all?section=subscriptions';
        }

        return (
          <NavLink
            end
            key={item.id}
            prefetch="intent"
            to={url}
            className={({isActive}) =>
              `text-white hover:text-orange-300 transition-colors duration-200 font-medium relative subheader ${
                isActive ? 'text-orange-300' : 'text-white'
              }`
            }
          >
            {item.title}
          </NavLink>
        );
      })}
    </nav>
  );
}

function HeaderCtas({
  isLoggedIn,
  cart,
}: Pick<HeaderProps, 'isLoggedIn' | 'cart'>) {
  return (
    <nav className="flex items-center space-x-4" role="navigation">
      <div className="hidden md:flex items-center space-x-4">
        <a
          href="https://bigriverbe.myshopify.com/pages/loyalty-program"
          target="_blank"
          rel="noopener noreferrer"
          className="text-white hover:text-orange-300 transition-colors duration-200 font-medium px-3 py-2 rounded-md border border-white/20 hover:border-orange-300/50"
        >
          Rewards
        </a>
        <NavLink
          prefetch="intent"
          to="/account"
          className="text-white hover:text-orange-300 transition-colors duration-200 font-medium"
        >
          <Suspense fallback="Sign in">
            <Await resolve={isLoggedIn} errorElement="Sign in">
              {(isLoggedIn) => (isLoggedIn ? 'Account' : 'Sign in')}
            </Await>
          </Suspense>
        </NavLink>
        <SearchToggle />
      </div>

      <CartToggle cart={cart} />

      <div className="md:hidden">
        <HeaderMenuMobileToggle />
      </div>
    </nav>
  );
}

function HeaderMenuMobileToggle() {
  const {open} = useAside();
  return (
    <button
      className="text-white hover:text-orange-300 transition-colors duration-200 p-2"
      onClick={() => open('mobile')}
      aria-label="Open mobile menu"
      style={{ color: 'white' }}
    >
      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
      </svg>
    </button>
  );
}

function SearchToggle() {
  const {open} = useAside();
  return (
    <button
      className="text-white hover:text-orange-300 transition-colors duration-200 p-2"
      onClick={() => open('search')}
      aria-label="Open search"
    >
      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
      </svg>
    </button>
  );
}

function CartBadge({count}: {count: number | null}) {
  const {open} = useAside();
  const {publish, shop, cart, prevCart} = useAnalytics();

  return (
    <button
      onClick={(e) => {
        e.preventDefault();
        open('cart');
        publish('cart_viewed', {
          cart,
          prevCart,
          shop,
          url: window.location.href || '',
        } as CartViewPayload);
      }}
      className="relative text-white hover:text-orange-300 transition-colors duration-200 p-2"
      aria-label={`Cart with ${count || 0} items`}
    >
      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m0 0h8" />
      </svg>
      {count !== null && count > 0 && (
        <span className="absolute -top-1 -right-1 bg-amber-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold">
          {count}
        </span>
      )}
    </button>
  );
}

function CartToggle({cart}: Pick<HeaderProps, 'cart'>) {
  return (
    <Suspense fallback={<CartBadge count={null} />}>
      <Await resolve={cart}>
        <CartBanner />
      </Await>
    </Suspense>
  );
}

function CartBanner() {
  const originalCart = useAsyncValue() as CartApiQueryFragment | null;
  const cart = useOptimisticCart(originalCart);
  return <CartBadge count={cart?.totalQuantity ?? 0} />;
}

const FALLBACK_HEADER_MENU = {
  id: 'gid://shopify/Menu/199655587896',
  items: [
    {
      id: 'gid://shopify/MenuItem/461609500728',
      resourceId: null,
      tags: [],
      title: 'Coffee',
      type: 'HTTP',
      url: '/collections/all',
      items: [],
    },
    {
      id: 'gid://shopify/MenuItem/461609500729',
      resourceId: null,
      tags: [],
      title: 'K-Cups',
      type: 'HTTP',
      url: '/collections/all?section=kcups',
      items: [],
    },
    {
      id: 'gid://shopify/MenuItem/461609500731',
      resourceId: null,
      tags: [],
      title: 'Subscriptions',
      type: 'HTTP',
      url: '/collections/all#section-subscriptions',
      items: [],
    },
    {
      id: 'gid://shopify/MenuItem/461609500730',
      resourceId: null,
      tags: [],
      title: 'Brew',
      type: 'HTTP',
      url: '/pages/brew',
      items: [],
    },
    {
      id: 'gid://shopify/MenuItem/461609599032',
      resourceId: 'gid://shopify/Page/92591030328',
      tags: [],
      title: 'Our Story',
      type: 'PAGE',
      url: '/our-story',
      items: [],
    },
    {
      id: 'gid://shopify/MenuItem/461609566264',
      resourceId: null,
      tags: [],
      title: 'Contact',
      type: 'HTTP',
      url: '/contact',
      items: [],
    },
    {
      id: 'gid://shopify/MenuItem/461609566265',
      resourceId: null,
      tags: [],
      title: 'Affiliate',
      type: 'HTTP',
      url: '/affiliate',
      items: [],
    },
  ],
};
