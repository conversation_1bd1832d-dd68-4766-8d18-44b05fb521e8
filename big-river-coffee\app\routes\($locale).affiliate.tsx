import { type MetaFunction } from 'react-router';
import { useEffect } from 'react';

export const meta: MetaFunction = () => {
  return [
    { title: 'Affiliate Program | Big River Coffee' },
    { description: 'Learn about our affiliate program and how you can earn commissions by promoting our premium coffee products.' }
  ];
};

export default function AffiliatePage() {
  // The affiliate link for Big River Coffee
  const affiliateLink = "https://www.shoutout.global/signup?id=9luhs";

  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Add body class for page-specific styling
    document.body.classList.add('affiliate');

    return () => {
      document.body.classList.remove('affiliate');
    };
  }, []);

  return (
    <>
    <style>{`
      body.affiliate {
        margin: 0 !important;
        padding: 0 !important;
        overflow-x: hidden;
      }
      body.affiliate main {
        padding: 0 !important;
        margin: 0 !important;
      }
    `}</style>

    <div className="relative overflow-hidden" style={{
      backgroundColor: '#eeedc1',
      margin: 0,
      padding: 0,
      width: '100vw',
      minHeight: '100vh'
    }}>
      {/* Background decorative circles */}
      <div className="absolute top-20 left-10 w-32 h-32 bg-army-600 rounded-full opacity-20"></div>
      <div className="absolute bottom-40 right-10 w-24 h-24 bg-army-600 rounded-full opacity-30"></div>
      <div className="absolute top-1/3 left-1/4 w-16 h-16 bg-army-600 rounded-full opacity-15"></div>
      <div className="absolute top-1/2 right-1/4 w-20 h-20 bg-army-600 rounded-full opacity-25"></div>
      <div className="absolute bottom-1/4 left-1/3 w-12 h-12 bg-army-600 rounded-full opacity-20"></div>
      <div className="absolute top-3/4 right-1/3 w-28 h-28 bg-army-600 rounded-full opacity-15"></div>

      {/* Hero Section */}
      <div className="relative overflow-hidden text-white z-10">
        {/* Mobile Hero */}
        <div className="block sm:hidden relative h-[55vh] min-h-[400px]">
          <div className="absolute inset-0 z-0">
            <img
              src="/3cups.jpg"
              alt="Coffee affiliate program background"
              className="w-full h-full object-cover object-center"
            />
          </div>
          <div className="relative z-10 max-w-lg mx-auto px-4 h-full flex items-center justify-center">
            <div className="text-center">
              <h1 className="text-3xl font-bold text-white drop-shadow-2xl mb-4" style={{ fontFamily: 'var(--font-title)' }}>Affiliate Program</h1>
              <p className="text-lg text-white drop-shadow-lg leading-relaxed" style={{ fontFamily: 'var(--font-body)' }}>
                Partner with us and earn commissions by promoting our premium coffee products
              </p>
            </div>
          </div>
        </div>

        {/* Desktop Hero */}
        <div className="hidden sm:block relative h-[60vh] lg:h-96">
          <div className="absolute inset-0 z-0">
            <img
              src="/3cups.jpg"
              alt="Coffee affiliate program background"
              className="w-full h-full object-cover object-center"
            />
          </div>
          <div className="relative z-10 w-full px-6 lg:px-8 h-full flex items-center justify-center">
            <div className="text-center">
              <h1 className="text-5xl lg:text-6xl xl:text-7xl font-bold text-white drop-shadow-2xl mb-4" style={{ fontFamily: 'var(--font-title)' }}>Affiliate Program</h1>
              <p className="text-xl lg:text-2xl text-white drop-shadow-lg max-w-3xl mx-auto" style={{ fontFamily: 'var(--font-body)' }}>
                Partner with us and earn commissions by promoting our premium coffee products
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="w-full px-4 sm:px-6 lg:px-8 py-16 relative z-10">
        <div className="w-full bg-army-600 p-8 md:p-12">
          {/* About the Affiliate Program */}
          <div className="mb-12">
            <h2 className="text-2xl md:text-3xl font-bold text-white mb-6" style={{ fontFamily: 'var(--font-title)' }}>About Our Affiliate Program</h2>
            <p className="text-lg text-white mb-6" style={{ fontFamily: 'var(--font-body)' }}>
              Big River Coffee has partnered with Shoutout to offer an affiliate program that allows you to earn commissions by promoting our premium coffee products on your website, blog, or social media channels.
            </p>
            <p className="text-lg text-white mb-6" style={{ fontFamily: 'var(--font-body)' }}>
              Shoutout is a trusted affiliate marketing platform that makes it easy to track your referrals and earn commissions. When someone clicks on your unique affiliate link and makes a purchase, you'll earn a commission on the sale.
            </p>
          </div>

          {/* Benefits */}
          <div className="mb-12">
            <h2 className="text-2xl md:text-3xl font-bold text-white mb-6" style={{ fontFamily: 'var(--font-title)' }}>Benefits of Our Affiliate Program</h2>
            <ul className="space-y-6">
              <li className="flex">
                <div className="flex-shrink-0 h-8 w-8 rounded-full bg-army-600 flex items-center justify-center shadow-sm">
                  <svg className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <div className="ml-4">
                  <p className="text-lg text-white">Competitive commission rates on all sales</p>
                </div>
              </li>
              <li className="flex">
                <div className="flex-shrink-0 h-8 w-8 rounded-full bg-army-600 flex items-center justify-center shadow-sm">
                  <svg className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <div className="ml-4">
                  <p className="text-lg text-white">Extended cookie duration to maximize your earnings</p>
                </div>
              </li>
              <li className="flex">
                <div className="flex-shrink-0 h-8 w-8 rounded-full bg-army-600 flex items-center justify-center shadow-sm">
                  <svg className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <div className="ml-4">
                  <p className="text-lg text-white">Access to promotional materials and banners</p>
                </div>
              </li>
              <li className="flex">
                <div className="flex-shrink-0 h-8 w-8 rounded-full bg-army-600 flex items-center justify-center shadow-sm">
                  <svg className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <div className="ml-4">
                  <p className="text-lg text-white">Detailed reporting and tracking of your performance</p>
                </div>
              </li>
              <li className="flex">
                <div className="flex-shrink-0 h-8 w-8 rounded-full bg-army-600 flex items-center justify-center shadow-sm">
                  <svg className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <div className="ml-4">
                  <p className="text-lg text-white">Regular payments for your referrals</p>
                </div>
              </li>
            </ul>
          </div>

          {/* How It Works */}
          <div className="mb-12">
            <h2 className="text-2xl md:text-3xl font-bold text-white mb-6" style={{ fontFamily: 'var(--font-title)' }}>How It Works</h2>
            <ol className="space-y-6">
              <li className="flex">
                <div className="flex-shrink-0 h-8 w-8 rounded-full bg-army-600 flex items-center justify-center text-white font-bold">
                  1
                </div>
                <div className="ml-4">
                  <h3 className="text-xl font-semibold text-white mb-2">Sign Up</h3>
                  <p className="text-lg text-white">
                    Join Shoutout and apply to become an affiliate for Big River Coffee.
                  </p>
                </div>
              </li>
              <li className="flex">
                <div className="flex-shrink-0 h-8 w-8 rounded-full bg-army-600 flex items-center justify-center text-white font-bold">
                  2
                </div>
                <div className="ml-4">
                  <h3 className="text-xl font-semibold text-white mb-2">Get Your Links</h3>
                  <p className="text-lg text-white">
                    Once approved, you'll receive your unique affiliate links and promotional materials.
                  </p>
                </div>
              </li>
              <li className="flex">
                <div className="flex-shrink-0 h-8 w-8 rounded-full bg-army-600 flex items-center justify-center text-white font-bold">
                  3
                </div>
                <div className="ml-4">
                  <h3 className="text-xl font-semibold text-white mb-2">Promote</h3>
                  <p className="text-lg text-white">
                    Share your affiliate links on your website, blog, social media, or email newsletters.
                  </p>
                </div>
              </li>
              <li className="flex">
                <div className="flex-shrink-0 h-8 w-8 rounded-full bg-army-600 flex items-center justify-center text-white font-bold">
                  4
                </div>
                <div className="ml-4">
                  <h3 className="text-xl font-semibold text-white mb-2">Earn</h3>
                  <p className="text-lg text-white">
                    Earn commissions on every sale made through your affiliate links.
                  </p>
                </div>
              </li>
            </ol>
          </div>

          {/* Join Now CTA */}
          <div className="relative rounded-2xl overflow-hidden p-12 text-center bg-gradient-to-br from-army-600 to-army-800 shadow-2xl">
            {/* Decorative background elements */}
            <div className="absolute top-8 left-8 w-20 h-20 bg-white rounded-full opacity-10"></div>
            <div className="absolute bottom-8 right-8 w-24 h-24 bg-orange-500 rounded-full opacity-15"></div>
            <div className="absolute top-1/2 left-16 w-12 h-12 bg-white rounded-full opacity-20"></div>
            <div className="absolute bottom-16 left-1/3 w-16 h-16 bg-orange-500 rounded-full opacity-12"></div>
            <div className="absolute top-12 right-1/4 w-14 h-14 bg-white rounded-full opacity-18"></div>

            {/* Content */}
            <div className="relative z-10 max-w-2xl mx-auto">
              <div className="mb-6">
                <span className="inline-block px-4 py-2 bg-orange-500 text-white text-sm font-semibold rounded-full mb-4" style={{ fontFamily: 'var(--font-header)' }}>
                  JOIN TODAY
                </span>
                <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-6 leading-tight" style={{ fontFamily: 'var(--font-title)' }}>
                  Ready to Start Earning?
                </h2>
                <div className="w-24 h-1 bg-orange-500 rounded mx-auto mb-8"></div>
              </div>

              <p className="text-lg md:text-xl text-white/90 mb-10 leading-relaxed" style={{ fontFamily: 'var(--font-body)' }}>
                Join our affiliate program today and start earning commissions by sharing premium coffee with your audience. It's free to join and easy to get started!
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <a
                  href={affiliateLink}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="group inline-flex items-center justify-center px-8 py-4 bg-orange-500 text-white rounded-xl font-semibold text-lg hover:bg-orange-600 hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl"
                  style={{ fontFamily: 'var(--font-header)' }}
                >
                  <span className="text-white">Join Affiliate Program</span>
                  <svg className="w-5 h-5 ml-2 text-white group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                  </svg>
                </a>

                <div className="text-center sm:text-left">
                  <p className="text-white/80 text-sm" style={{ fontFamily: 'var(--font-body)' }}>
                    Free to join • No monthly fees
                  </p>
                  <p className="text-orange-300 text-sm font-semibold" style={{ fontFamily: 'var(--font-header)' }}>
                    Start earning today!
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    </>
  );
}
